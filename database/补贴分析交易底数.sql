--补贴分析交易底数
USE [CAMDB]
GO
DECLARE @DataDate bigint=20250802
select t.*,
ISNULL(p.拼好饭订单量,0) 拼好饭订单量,
剔除PHF后餐饮订单量=t.餐饮订单量-ISNULL(p.拼好饭订单量,0) 
from(
select CityName 城市名称,
是否三区城市=(CASE WHEN CityName in ('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县') then 1 else 0 END),
DataDate 日期,
餐饮订单量=SUM(CASE WHEN Category='餐饮' THEN OrderCnt ELSE 0 END),
餐饮实付交易额 =SUM(CASE WHEN Category='餐饮' THEN RealCollect ELSE 0 END) ,
非餐实付交易额 =SUM(CASE WHEN Category<>'餐饮' AND One_Category<>'数码家电' THEN RealCollect ELSE 0 END) ,
数码家电实付交易额 =SUM(CASE WHEN Category<>'餐饮' AND One_Category='数码家电' THEN RealCollect ELSE 0 END),
餐饮原价交易额=SUM(CASE WHEN Category='餐饮' THEN OrginalCost ELSE 0 END) , 
非餐原价交易额=SUM(CASE WHEN Category<>'餐饮' THEN OrginalCost ELSE 0 END) 
from tbCateringRealCollect_New
where DataYear=2025 
AND DataDate>=@DataDate
AND CityName NOT in('廉江市','阳西')
group by CityName,
DataDate
)t
left join (
select CityName 城市名称,DataDate 日期,SUM(PHFOrderCnt) 拼好饭订单量 from tbPHFOrderCnt
where DataYear=2025 
AND CityName in('鹤山市','恩平市','万宁','雷州市','吴川市','徐闻县','海丰县','雄县','阳江市')
group by CityName,DataDate
)p on t.城市名称=p.城市名称 and t.日期=p.日期